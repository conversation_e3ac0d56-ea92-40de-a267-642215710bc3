✅ RN1 — Mensagem de verificação de email
Componente: tela de login
Ação: Aumentar o tempo de exibição da mensagem:

"Verifique seu email antes de logar"
Objetivo: Melhorar visibilidade e evitar que o usuário perca o aviso rapidamente.

✅ RN2 — Integração de endpoints de verificação
Componentes: tela de login e cadastro
Ações:

Integrar endpoint resend verification

Integrar endpoint verify status
Objetivo: Permitir que o usuário reenviar verificação e validar se já confirmou o email.

✅ RN3 — Ícone de olho no input de senha
Componentes: todos os inputs de senha (login, cadastro, redefinir senha)
Ação: Corrigir alinhamento do ícone de olho
Objetivo: Garantir aparência consistente e clicável.

✅ RN4 — Botão "Voltar para login"
Componentes: modais e páginas relacionadas à autenticação (ex: redefinir senha, erro de verificação, etc)
Ação: Adicionar botão "Voltar para o login"
Objetivo: Facilitar navegação do usuário

✅ RN5 — Alterar senha somente se verificado
Componentes: tela de recuperação de senha
Ação: Caso a conta não esteja verificada, exibir mensagem clara e bloquear alteração
Mensagem sugerida: "Você precisa verificar seu email antes de alterar sua senha."

✅ RN6 — Página inicial '/' (pós login)
Componentes: Header, Home
Ações:

Mostrar botão "Criar Postagem" apenas se usuário for admin ou super-admin

Remover botão "Criar Conta" se usuário estiver logado
Objetivo: Exibir ações apenas relevantes ao tipo de usuário

✅ RN7 — Página de posts '/posts'
Componentes: PostList, SearchBar, Filter
Ações:

Impedir busca automática ao digitar (remover debounce/get por input)

A busca só deve ocorrer ao clicar no botão "Buscar"

O mesmo vale para filtros e botão de limpar filtros

✅ RN8 — Menu “Dashboard” → “Gerenciar”
Componentes: Header, Menu
Ações:

Renomear item de menu de “Dashboard” para “Gerenciar”

Mostrar o botão "Gerenciar" apenas para usuários com permissão admin ou superior

✅ RN9 — Proteção da rota '/gerenciar'
Componente: Route Guard, GerenciarPage
Ação:

Caso o usuário não seja admin ou superior, redirecionar automaticamente para '/'

✅ RN10 — Página '/gerenciar'
Componentes: UserTable, SearchBar, UserActions
Ações:

1. Campo de busca por email
   Criar um input funcional para buscar usuários por email

2. Tabela de usuários
   Campos obrigatórios:

nome, email, cargo, data de criação

Coluna de ações exibida apenas se usuário logado for admin ou superior

3. Permissões de ações por tipo de usuário:
   👑 Super Admin
   Excluir contas de membros

Promover membros a admin

Remover cargo de admin

🛠️ Admin Normal
Pesquisar usuários

Alterar nome de usuários

Excluir somente contas de usuários comuns (não pode excluir outros admins ou super-admins)

4. Ações e ícones
   Tabela deve adaptar dinamicamente os ícones e botões visíveis com base na permissão do usuário logado
