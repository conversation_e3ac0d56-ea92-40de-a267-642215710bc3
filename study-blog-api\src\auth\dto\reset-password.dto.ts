import { IsString, Min<PERSON>ength, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ResetPasswordDto {
  @ApiProperty({
    description: 'Token de redefinição de senha',
    example: 'abc123def456',
  })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({
    description: 'Nova senha (mínimo 6 caracteres)',
    example: 'novasenha123',
    minLength: 6,
  })
  @IsString()
  @MinLength(6, { message: '<PERSON><PERSON> deve ter pelo menos 6 caracteres' })
  password: string;
}
