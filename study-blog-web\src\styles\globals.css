@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  color: #000000;
  background: #ffffff;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

/* Custom styles for line-clamp */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Forçar cores claras para inputs */
input, textarea, select {
  color: #000000 !important;
  background-color: #ffffff !important;
}

input:focus, textarea:focus, select:focus {
  color: #000000 !important;
  background-color: #ffffff !important;
}

/* Remover tema escuro automático */
@media (prefers-color-scheme: dark) {
  html {
    color-scheme: light;
  }
  body {
    color: #000000;
    background: #ffffff;
  }
}
