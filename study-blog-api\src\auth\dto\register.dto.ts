import { IsEmail, IsString, <PERSON><PERSON><PERSON>th, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class RegisterDto {
  @ApiProperty({
    description: 'Endereço de email do usuário',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Forneça um endereço de email válido' })
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Nome completo do usuário',
    example: '<PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: '<PERSON><PERSON> do usuário (mínimo 6 caracteres)',
    example: 'senha123',
    minLength: 6,
  })
  @IsString()
  @MinLength(6, { message: '<PERSON>ha deve ter pelo menos 6 caracteres' })
  password: string;
}
