{"name": "study-blog-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@tailwindcss/line-clamp": "^0.4.4", "axios": "^1.10.0", "clsx": "^2.1.1", "lucide-react": "^0.523.0", "next": "15.3.4", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.58.1", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "24.0.4", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "autoprefixer": "^10.4.0", "eslint": "9.29.0", "eslint-config-next": "15.3.4", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.0", "postcss": "^8.4.0", "prettier": "^3.6.0", "tailwindcss": "^3.4.0", "typescript": "5.8.3"}}